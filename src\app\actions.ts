// src/app/actions.ts
"use server";

import { auth } from "@/lib/firebase";
import { db } from "@/lib/firebase";
import { doc, getDoc, setDoc, collection, query, where, getDocs, orderBy } from "firebase/firestore";
import type { BrandProfile, BrandAnalysisResult, GeneratedPost } from "@/lib/types";

// Website analysis action
export async function analyzeBrandAction(userId: string, websiteUrl: string): Promise<BrandAnalysisResult> {
  try {
    console.log("🔍 Starting website analysis for:", websiteUrl);

    // Call your AI analysis API here
    // For now, return mock data
    const mockResult: BrandAnalysisResult = {
      businessName: "Sample Business",
      businessType: "Technology Company",
      location: "San Francisco, CA",
      description: "A cutting-edge technology company focused on innovation and customer success.",
      services: "Software Development\nConsulting Services\nTechnical Support",
      targetAudience: "Small to medium businesses looking for technology solutions",
      keyFeatures: "24/7 Support\nScalable Solutions\nExpert Team",
      competitiveAdvantages: "Industry Experience\nCustomer-First Approach\nInnovative Technology",
      visualStyle: "Modern, clean, professional",
      writingTone: "Professional yet approachable",
      contentThemes: "Innovation, reliability, customer success",
      primaryColor: "#3399FF",
      accentColor: "#FF6B35",
      backgroundColor: "#FFFFFF",
      contactInfo: {
        phone: "(*************",
        email: "<EMAIL>",
        address: "123 Tech Street, San Francisco, CA 94105"
      }
    };

    console.log("✅ Website analysis completed");
    return mockResult;
  } catch (error) {
    console.error("❌ Website analysis failed:", error);
    throw new Error("Failed to analyze website");
  }
}

// Save brand profile action
export async function saveBrandProfile(userId: string, profile: BrandProfile): Promise<void> {
  try {
    const profileRef = doc(db, 'brandProfiles', userId);
    await setDoc(profileRef, profile, { merge: true });
    console.log("✅ Brand profile saved successfully");
  } catch (error) {
    console.error("❌ Failed to save brand profile:", error);
    throw new Error("Failed to save brand profile");
  }
}

// Get brand profile action
export async function getBrandProfile(userId: string): Promise<BrandProfile | null> {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.error("No authenticated user found");
      throw new Error("User not authenticated");
    }

    const profileRef = doc(db, "profiles", userId);
    const profileSnap = await getDoc(profileRef);

    if (profileSnap.exists()) {
      console.log("✅ Brand profile fetched successfully");
      return profileSnap.data() as BrandProfile;
    } else {
      console.log("No brand profile found for user:", userId);
      return null;
    }
  } catch (error) {
    console.error("❌ Failed to fetch brand profile:", error);
    throw error;
  }
}

// Save post action
export async function savePost(userId: string, post: GeneratedPost): Promise<void> {
  try {
    const postsRef = collection(db, 'posts');
    const postDoc = doc(postsRef, post.id);
    await setDoc(postDoc, { ...post, userId }, { merge: true });
    console.log("✅ Post saved successfully");
  } catch (error) {
    console.error("❌ Failed to save post:", error);
    throw new Error("Failed to save post");
  }
}

// Get posts action
export async function getPosts(userId: string): Promise<GeneratedPost[]> {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      console.error("No authenticated user found");
      throw new Error("User not authenticated");
    }

    const postsRef = collection(db, "posts");
    const q = query(
      postsRef,
      where("userId", "==", userId),
      orderBy("date", "desc")
    );

    const querySnapshot = await getDocs(q);
    const posts: GeneratedPost[] = [];

    querySnapshot.forEach((doc) => {
      posts.push(doc.data() as GeneratedPost);
    });
  
    console.log(`✅ Fetched ${posts.length} posts successfully`);
    return posts;
  } catch (error) {
    console.error("❌ Failed to fetch posts:", error);
    throw error;
  }
}

// Analyze website with AI (enhanced version)
export async function analyzeWebsiteAction(userId: string, websiteUrl: string): Promise<BrandAnalysisResult> {
  try {
    console.log("🔍 Starting enhanced website analysis for:", websiteUrl);

    // Here you would integrate with your AI service (OpenAI, Claude, etc.)
    // For now, we'll return enhanced mock data based on the URL

    const domain = new URL(websiteUrl).hostname;
    const businessName = domain.split('.')[0].charAt(0).toUpperCase() + domain.split('.')[0].slice(1);

    const mockResult: BrandAnalysisResult = {
      businessName: businessName,
      businessType: "Professional Services",
      location: "United States",
      description: `${businessName} is a forward-thinking company dedicated to delivering exceptional value to our clients through innovative solutions and outstanding service.`,
      services: "Consulting Services\nStrategy Development\nImplementation Support\nOngoing Maintenance",
      targetAudience: "Business professionals and organizations seeking reliable, high-quality solutions",
      keyFeatures: "Expert Team\nProven Track Record\nCustomized Solutions\nOngoing Support",
      competitiveAdvantages: "Industry Expertise\nClient-Focused Approach\nInnovative Methods\nReliable Delivery",
      visualStyle: "Professional, modern, trustworthy",
      writingTone: "Professional, confident, approachable",
      contentThemes: "Excellence, innovation, reliability, partnership",
      primaryColor: "#2563EB",
      accentColor: "#F59E0B",
      backgroundColor: "#FFFFFF",
      contactInfo: {
        phone: "",
        email: "",
        address: ""
      }
    };

    console.log("✅ Enhanced website analysis completed");
    return mockResult;
  } catch (error) {
    console.error("❌ Enhanced website analysis failed:", error);
    throw new Error("Failed to analyze website");
  }
}
