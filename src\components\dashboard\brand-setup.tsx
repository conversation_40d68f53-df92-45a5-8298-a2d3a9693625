// src/components/dashboard/brand-setup.tsx
"use client";

import * as React from "react";
import { useToast } from "@/hooks/use-toast";
import type { BrandProfile } from "@/lib/types";
import { analyzeBrandAction } from "@/app/actions";
import { useAuthState } from "react-firebase-hooks/auth";
import { auth } from "@/lib/firebase";
import { WebsiteStep, BrandDetailsStep, LogoStep } from "./brand-setup-steps";

// Helper to convert hex to HSL string
const hexToHslString = (hex: string): string => {
  hex = hex.replace(/^#/, '');
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0, s = 0, l = (max + min) / 2;
  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }
  return `${(h * 360).toFixed(1)} ${(s * 100).toFixed(1)}% ${(l * 100).toFixed(1)}%`;
};

// Helper to convert HSL string to hex
const hslStringToHex = (hslStr: string): string => {
  if (!hslStr || typeof hslStr !== 'string') return "#000000";
  const parts = hslStr.match(/(\d+\.?\d*)/g);
  if (!parts || parts.length < 3) return "#000000";

  const [h, s, l] = parts.map(Number);
  const s_norm = s / 100;
  const l_norm = l / 100;
  let c = (1 - Math.abs(2 * l_norm - 1)) * s_norm,
    x = c * (1 - Math.abs((h / 60) % 2 - 1)),
    m = l_norm - c / 2,
    r = 0, g = 0, b = 0;
  if (0 <= h && h < 60) { [r, g, b] = [c, x, 0]; }
  else if (60 <= h && h < 120) { [r, g, b] = [x, c, 0]; }
  else if (120 <= h && h < 180) { [r, g, b] = [0, c, x]; }
  else if (180 <= h && h < 240) { [r, g, b] = [0, x, c]; }
  else if (240 <= h && h < 300) { [r, g, b] = [x, 0, c]; }
  else if (300 <= h && h < 360) { [r, g, b] = [c, 0, x]; }
  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
}

type BrandSetupProps = {
  initialProfile: BrandProfile | null;
  onProfileSaved: (profile: BrandProfile) => void;
};

export function BrandSetup({ initialProfile, onProfileSaved }: BrandSetupProps) {
  const [user] = useAuthState(auth);
  const [currentStep, setCurrentStep] = React.useState<'website' | 'details' | 'logo'>(
    initialProfile ? 'details' : 'website'
  );
  const [isSaving, setIsSaving] = React.useState(false);
  const [isAnalyzing, setIsAnalyzing] = React.useState(false);
  const [websiteUrl, setWebsiteUrl] = React.useState(initialProfile?.websiteUrl || '');
  const [brandData, setBrandData] = React.useState<any>(null);
  const [logoDataUrl, setLogoDataUrl] = React.useState<string>(initialProfile?.logoDataUrl || "");
  const { toast } = useToast();

  // Initialize brand data from existing profile
  React.useEffect(() => {
    if (initialProfile) {
      setBrandData({
        businessName: initialProfile.businessName || "",
        businessType: initialProfile.businessType || "",
        location: initialProfile.location || "",
        description: initialProfile.description || "",
        services: initialProfile.services || "",
        targetAudience: initialProfile.targetAudience || "",
        keyFeatures: initialProfile.keyFeatures || "",
        competitiveAdvantages: initialProfile.competitiveAdvantages || "",
        contactPhone: initialProfile.contactInfo?.phone || "",
        contactEmail: initialProfile.contactInfo?.email || "",
        contactAddress: initialProfile.contactInfo?.address || "",
        visualStyle: initialProfile.visualStyle || "",
        writingTone: initialProfile.writingTone || "",
        contentThemes: initialProfile.contentThemes || "",
        primaryColor: initialProfile.primaryColor ? hslStringToHex(initialProfile.primaryColor) : "#3399FF",
        accentColor: initialProfile.accentColor ? hslStringToHex(initialProfile.accentColor) : "#FF6B35",
        backgroundColor: initialProfile.backgroundColor ? hslStringToHex(initialProfile.backgroundColor) : "#FFFFFF",
        socialFacebook: initialProfile.socialMedia?.facebook || "",
        socialInstagram: initialProfile.socialMedia?.instagram || "",
        socialTwitter: initialProfile.socialMedia?.twitter || "",
        socialLinkedin: initialProfile.socialMedia?.linkedin || "",
      });
      setWebsiteUrl(initialProfile.websiteUrl || '');
    }
  }, [initialProfile]);

  // Website analysis handler
  const handleWebsiteAnalysis = async (url: string) => {
    if (!user) return;

    setIsAnalyzing(true);
    setWebsiteUrl(url);
    
    try {
      console.log("🔍 Analyzing website:", url);
      toast({
        title: "Analyzing Website",
        description: "AI is extracting information from your website...",
      });

      const result = await analyzeBrandAction(user.uid, url);
      console.log("✅ Analysis result:", result);

      if (!result) {
        throw new Error("No analysis result returned");
      }

      setBrandData({
        businessName: result.businessName || '',
        businessType: result.businessType || '',
        location: result.location || '',
        description: result.description || '',
        services: result.services || '',
        targetAudience: result.targetAudience || '',
        keyFeatures: result.keyFeatures || '',
        competitiveAdvantages: result.competitiveAdvantages || '',
        contactPhone: result.contactInfo?.phone || '',
        contactEmail: result.contactInfo?.email || '',
        contactAddress: result.contactInfo?.address || '',
        visualStyle: result.visualStyle || '',
        writingTone: result.writingTone || '',
        contentThemes: result.contentThemes || '',
        primaryColor: result.primaryColor || '#3399FF',
        accentColor: result.accentColor || '#FF6B35',
        backgroundColor: result.backgroundColor || '#FFFFFF',
        socialFacebook: '',
        socialInstagram: '',
        socialTwitter: '',
        socialLinkedin: '',
      });

      toast({
        title: "Analysis Complete!",
        description: "Your website has been analyzed. Review and edit the information below.",
      });

      setCurrentStep('details');
    } catch (error) {
      console.error("❌ Analysis failed:", error);
      toast({
        variant: "destructive",
        title: "Analysis Failed",
        description: "Could not analyze the website. You can still fill in the information manually.",
      });
      
      // Still proceed to details step but with empty data
      setBrandData({
        businessName: '',
        businessType: '',
        location: '',
        description: '',
        services: '',
        targetAudience: '',
        keyFeatures: '',
        competitiveAdvantages: '',
        contactPhone: '',
        contactEmail: '',
        contactAddress: '',
        visualStyle: '',
        writingTone: '',
        contentThemes: '',
        primaryColor: '#3399FF',
        accentColor: '#FF6B35',
        backgroundColor: '#FFFFFF',
        socialFacebook: '',
        socialInstagram: '',
        socialTwitter: '',
        socialLinkedin: '',
      });
      setCurrentStep('details');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // Brand details save handler
  const handleBrandDetailsSave = (data: any) => {
    setBrandData(data);
    setCurrentStep('logo');
  };

  // Logo save handler
  const handleLogoSave = (logoDataUrl: string | null) => {
    setLogoDataUrl(logoDataUrl || '');
    handleFinalSave(logoDataUrl);
  };

  // Final save handler
  const handleFinalSave = (finalLogoDataUrl?: string | null) => {
    if (!brandData) return;

    const profile: BrandProfile = {
      ...brandData,
      websiteUrl,
      logoDataUrl: finalLogoDataUrl !== undefined ? finalLogoDataUrl || '' : logoDataUrl,
      primaryColor: brandData.primaryColor ? hexToHslString(brandData.primaryColor) : undefined,
      accentColor: brandData.accentColor ? hexToHslString(brandData.accentColor) : undefined,
      backgroundColor: brandData.backgroundColor ? hexToHslString(brandData.backgroundColor) : undefined,
      contactInfo: {
        phone: brandData.contactPhone,
        email: brandData.contactEmail,
        address: brandData.contactAddress,
      },
      socialMedia: {
        facebook: brandData.socialFacebook,
        instagram: brandData.socialInstagram,
        twitter: brandData.socialTwitter,
        linkedin: brandData.socialLinkedin,
      },
    };

    setIsSaving(true);
    try {
      onProfileSaved(profile);
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Failed to save profile",
        description: (error as Error).message,
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Render the appropriate step
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'website':
        return (
          <WebsiteStep
            onNext={handleWebsiteAnalysis}
            isAnalyzing={isAnalyzing}
          />
        );
      
      case 'details':
        return (
          <BrandDetailsStep
            brandData={brandData}
            onSave={handleBrandDetailsSave}
            onBack={() => setCurrentStep('website')}
            isSaving={false}
          />
        );
      
      case 'logo':
        return (
          <LogoStep
            onSave={handleLogoSave}
            onBack={() => setCurrentStep('details')}
            isSaving={isSaving}
            currentLogo={logoDataUrl}
          />
        );
      
      default:
        return null;
    }
  };

  return (
    <div className="mx-auto max-w-6xl p-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold font-headline mb-2">
          {initialProfile ? "Update Brand Profile" : "Create Your Brand Profile"}
        </h1>
        <p className="text-muted-foreground">
          {currentStep === 'website' && "Let's start by analyzing your website"}
          {currentStep === 'details' && "Review and edit your brand information"}
          {currentStep === 'logo' && "Upload your logo and finalize"}
        </p>
      </div>

      {/* Progress indicator */}
      <div className="flex justify-center mb-8">
        <div className="flex items-center space-x-4">
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            currentStep === 'website' ? 'bg-primary text-primary-foreground' :
            ['details', 'logo'].includes(currentStep) ? 'bg-primary text-primary-foreground' : 'bg-muted'
          }`}>
            1
          </div>
          <div className={`w-12 h-0.5 ${
            ['details', 'logo'].includes(currentStep) ? 'bg-primary' : 'bg-muted'
          }`} />
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            currentStep === 'details' ? 'bg-primary text-primary-foreground' :
            currentStep === 'logo' ? 'bg-primary text-primary-foreground' : 'bg-muted'
          }`}>
            2
          </div>
          <div className={`w-12 h-0.5 ${
            currentStep === 'logo' ? 'bg-primary' : 'bg-muted'
          }`} />
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            currentStep === 'logo' ? 'bg-primary text-primary-foreground' : 'bg-muted'
          }`}>
            3
          </div>
        </div>
      </div>

      {/* Step content */}
      {renderCurrentStep()}
    </div>
  );
}
