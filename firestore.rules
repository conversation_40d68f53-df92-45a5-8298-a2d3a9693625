rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read and write their own profile
    match /profiles/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to read and write their own posts
    match /posts/{postId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Allow authenticated users to create posts
    match /posts/{postId} {
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Allow authenticated users to read and write their own creative assets
    match /creativeAssets/{assetId} {
      allow read, write: if request.auth != null && request.auth.uid == resource.data.userId;
    }
    
    // Allow authenticated users to create creative assets
    match /creativeAssets/{assetId} {
      allow create: if request.auth != null && request.auth.uid == request.resource.data.userId;
    }
    
    // Temporary: Allow all authenticated users to read/write test documents
    match /test/{document=**} {
      allow read, write: if request.auth != null;
    }
    
    // For development only - remove in production
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
